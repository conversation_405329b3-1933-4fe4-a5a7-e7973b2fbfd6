import sqlite3

def check_fusion_tables():
    try:
        conn = sqlite3.connect('data/lottery.db')
        cursor = conn.cursor()
        
        # 检查fusion相关表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND (name LIKE '%fusion%' OR name LIKE '%final%' OR name LIKE '%prediction_performance%')")
        tables = cursor.fetchall()
        
        print("Fusion tables found:")
        for table in tables:
            print(f"  - {table[0]}")
        
        # 检查权重配置
        cursor.execute("SELECT COUNT(*) FROM fusion_weights")
        weight_count = cursor.fetchone()[0]
        print(f"Weight configurations: {weight_count}")
        
        # 检查约束规则
        cursor.execute("SELECT COUNT(*) FROM fusion_constraint_rules")
        rule_count = cursor.fetchone()[0]
        print(f"Constraint rules: {rule_count}")
        
        conn.close()
        return len(tables) > 0
        
    except Exception as e:
        print(f"Error: {e}")
        return False

if __name__ == '__main__':
    success = check_fusion_tables()
    print(f"Fusion tables exist: {success}")
