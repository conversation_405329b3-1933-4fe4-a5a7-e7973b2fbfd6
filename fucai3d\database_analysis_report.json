{"database_analysis": {"data/alerts.db": {"file_path": "data/alerts.db", "table_count": 2, "tables": {"alerts": {"columns": [{"name": "id", "type": "INTEGER", "not_null": false, "primary_key": true}, {"name": "alert_type", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "level", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "message", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "timestamp", "type": "DATETIME", "not_null": false, "primary_key": false}], "record_count": 1}, "sqlite_sequence": {"columns": [{"name": "name", "type": "", "not_null": false, "primary_key": false}, {"name": "seq", "type": "", "not_null": false, "primary_key": false}], "record_count": 1}}}, "data/fucai3d.db": {"file_path": "data/fucai3d.db", "table_count": 15, "tables": {"optimization_logs": {"columns": [{"name": "id", "type": "INTEGER", "not_null": false, "primary_key": true}, {"name": "optimization_type", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "trigger_reason", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "component_name", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "start_time", "type": "TIMESTAMP", "not_null": true, "primary_key": false}, {"name": "end_time", "type": "TIMESTAMP", "not_null": false, "primary_key": false}, {"name": "status", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "details", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "performance_before", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "performance_after", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "improvement_score", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "rollback_reason", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "primary_key": false}, {"name": "updated_at", "type": "TIMESTAMP", "not_null": false, "primary_key": false}], "record_count": 0}, "sqlite_sequence": {"columns": [{"name": "name", "type": "", "not_null": false, "primary_key": false}, {"name": "seq", "type": "", "not_null": false, "primary_key": false}], "record_count": 3}, "auto_optimization_config": {"columns": [{"name": "id", "type": "INTEGER", "not_null": false, "primary_key": true}, {"name": "config_name", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "config_type", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "component_name", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "config_value", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "default_value", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "min_value", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "max_value", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "is_active", "type": "BOOLEAN", "not_null": false, "primary_key": false}, {"name": "auto_adjust", "type": "BOOLEAN", "not_null": false, "primary_key": false}, {"name": "last_updated", "type": "TIMESTAMP", "not_null": false, "primary_key": false}, {"name": "updated_by", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "update_reason", "type": "TEXT", "not_null": false, "primary_key": false}], "record_count": 0}, "enhanced_performance_monitor": {"columns": [{"name": "id", "type": "INTEGER", "not_null": false, "primary_key": true}, {"name": "metric_id", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "metric_name", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "metric_value", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "metric_category", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "component_name", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "threshold_warning", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "threshold_critical", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "status", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "trend_direction", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "change_percentage", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "collection_time", "type": "TIMESTAMP", "not_null": false, "primary_key": false}, {"name": "metadata", "type": "TEXT", "not_null": false, "primary_key": false}], "record_count": 0}, "performance_thresholds_config": {"columns": [{"name": "id", "type": "INTEGER", "not_null": false, "primary_key": true}, {"name": "metric_name", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "warning_threshold", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "critical_threshold", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "comparison_operator", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "enabled", "type": "BOOLEAN", "not_null": false, "primary_key": false}, {"name": "auto_adjust", "type": "BOOLEAN", "not_null": false, "primary_key": false}, {"name": "baseline_period_days", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "primary_key": false}, {"name": "updated_at", "type": "TIMESTAMP", "not_null": false, "primary_key": false}], "record_count": 0}, "dashboard_config": {"columns": [{"name": "id", "type": "INTEGER", "not_null": false, "primary_key": true}, {"name": "dashboard_name", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "metric_ids", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "layout_config", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "refresh_interval", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "enabled", "type": "BOOLEAN", "not_null": false, "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "primary_key": false}], "record_count": 0}, "hundreds_predictions": {"columns": [{"name": "id", "type": "INTEGER", "not_null": false, "primary_key": true}, {"name": "issue", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "model_type", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "prob_0", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "prob_1", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "prob_2", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "prob_3", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "prob_4", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "prob_5", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "prob_6", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "prob_7", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "prob_8", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "prob_9", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "predicted_digit", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "confidence", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "feature_count", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "training_samples", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "primary_key": false}], "record_count": 0}, "hundreds_model_performance": {"columns": [{"name": "id", "type": "INTEGER", "not_null": false, "primary_key": true}, {"name": "model_type", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "evaluation_period", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "accuracy", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "top3_accuracy", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "avg_confidence", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "precision_per_digit", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "recall_per_digit", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "f1_score_per_digit", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "confusion_matrix", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "feature_importance", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "training_time", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "prediction_time", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "model_size", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "evaluated_at", "type": "TIMESTAMP", "not_null": false, "primary_key": false}], "record_count": 0}, "final_predictions": {"columns": [{"name": "id", "type": "INTEGER", "not_null": false, "primary_key": true}, {"name": "issue", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "prediction_rank", "type": "INTEGER", "not_null": true, "primary_key": false}, {"name": "hundreds", "type": "INTEGER", "not_null": true, "primary_key": false}, {"name": "tens", "type": "INTEGER", "not_null": true, "primary_key": false}, {"name": "units", "type": "INTEGER", "not_null": true, "primary_key": false}, {"name": "sum_value", "type": "INTEGER", "not_null": true, "primary_key": false}, {"name": "span_value", "type": "INTEGER", "not_null": true, "primary_key": false}, {"name": "combined_probability", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "hundreds_prob", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "tens_prob", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "units_prob", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "sum_prob", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "span_prob", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "sum_consistency", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "span_consistency", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "constraint_score", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "diversity_score", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "confidence_level", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "fusion_method", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "ranking_strategy", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "primary_key": false}], "record_count": 20}, "fusion_weights": {"columns": [{"name": "id", "type": "INTEGER", "not_null": false, "primary_key": true}, {"name": "weight_type", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "predictor_name", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "model_name", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "weight_value", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "performance_score", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "accuracy_rate", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "confidence_score", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "is_active", "type": "BOOLEAN", "not_null": false, "primary_key": false}, {"name": "last_updated", "type": "TIMESTAMP", "not_null": false, "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "primary_key": false}], "record_count": 6}, "prediction_performance": {"columns": [{"name": "id", "type": "INTEGER", "not_null": false, "primary_key": true}, {"name": "issue", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "actual_hundreds", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "actual_tens", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "actual_units", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "actual_sum", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "actual_span", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "predicted_rank", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "hit_type", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "hundreds_accuracy", "type": "BOOLEAN", "not_null": false, "primary_key": false}, {"name": "tens_accuracy", "type": "BOOLEAN", "not_null": false, "primary_key": false}, {"name": "units_accuracy", "type": "BOOLEAN", "not_null": false, "primary_key": false}, {"name": "sum_accuracy", "type": "BOOLEAN", "not_null": false, "primary_key": false}, {"name": "span_accuracy", "type": "BOOLEAN", "not_null": false, "primary_key": false}, {"name": "overall_score", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "probability_score", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "constraint_score", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "diversity_effectiveness", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "fusion_method", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "ranking_strategy", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "top_k_hit", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "confidence_accuracy", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "evaluated_at", "type": "TIMESTAMP", "not_null": false, "primary_key": false}], "record_count": 0}, "fusion_constraint_rules": {"columns": [{"name": "id", "type": "INTEGER", "not_null": false, "primary_key": true}, {"name": "rule_name", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "rule_type", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "rule_expression", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "weight", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "tolerance", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "is_active", "type": "BOOLEAN", "not_null": false, "primary_key": false}, {"name": "priority", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "primary_key": false}], "record_count": 5}, "fusion_sessions": {"columns": [{"name": "id", "type": "INTEGER", "not_null": false, "primary_key": true}, {"name": "session_id", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "issue", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "fusion_method", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "ranking_strategy", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "input_data", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "output_data", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "execution_time", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "success", "type": "BOOLEAN", "not_null": false, "primary_key": false}, {"name": "error_message", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "primary_key": false}], "record_count": 0}, "weight_history": {"columns": [{"name": "id", "type": "INTEGER", "not_null": false, "primary_key": true}, {"name": "timestamp", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "weights", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "performance_summary", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "adjustment_reason", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "primary_key": false}], "record_count": 0}, "fusion_statistics": {"columns": [{"name": "id", "type": "INTEGER", "not_null": false, "primary_key": true}, {"name": "date", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "total_predictions", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "successful_predictions", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "top_1_hits", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "top_5_hits", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "top_10_hits", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "average_execution_time", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "average_confidence", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "most_used_fusion_method", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "most_used_ranking_strategy", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "primary_key": false}], "record_count": 0}}}, "data/lottery.db": {"file_path": "data/lottery.db", "table_count": 22, "tables": {"lottery_data": {"columns": [{"name": "id", "type": "INTEGER", "not_null": false, "primary_key": true}, {"name": "issue", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "draw_date", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "hundreds", "type": "INTEGER", "not_null": true, "primary_key": false}, {"name": "tens", "type": "INTEGER", "not_null": true, "primary_key": false}, {"name": "units", "type": "INTEGER", "not_null": true, "primary_key": false}, {"name": "trial_hundreds", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "trial_tens", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "trial_units", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "machine_number", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "sales_amount", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "prize_info", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "sum_value", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "span", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "number_type", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "primary_key": false}, {"name": "updated_at", "type": "TIMESTAMP", "not_null": false, "primary_key": false}], "record_count": 8359}, "sqlite_sequence": {"columns": [{"name": "name", "type": "", "not_null": false, "primary_key": false}, {"name": "seq", "type": "", "not_null": false, "primary_key": false}], "record_count": 7}, "collection_logs": {"columns": [{"name": "id", "type": "INTEGER", "not_null": false, "primary_key": true}, {"name": "collection_time", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "source_url", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "records_collected", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "success", "type": "BOOLEAN", "not_null": false, "primary_key": false}, {"name": "error_message", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "response_time", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "primary_key": false}], "record_count": 0}, "data_validation": {"columns": [{"name": "id", "type": "INTEGER", "not_null": false, "primary_key": true}, {"name": "validation_time", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "issue", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "validation_type", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "is_valid", "type": "BOOLEAN", "not_null": false, "primary_key": false}, {"name": "error_details", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "primary_key": false}], "record_count": 0}, "lottery_records": {"columns": [{"name": "id", "type": "INTEGER", "not_null": false, "primary_key": true}, {"name": "period", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "date", "type": "DATE", "not_null": true, "primary_key": false}, {"name": "numbers", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "trial_numbers", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "draw_machine", "type": "INTEGER", "not_null": true, "primary_key": false}, {"name": "trial_machine", "type": "INTEGER", "not_null": true, "primary_key": false}, {"name": "sales_amount", "type": "INTEGER", "not_null": true, "primary_key": false}, {"name": "direct_prize", "type": "INTEGER", "not_null": true, "primary_key": false}, {"name": "group3_prize", "type": "INTEGER", "not_null": true, "primary_key": false}, {"name": "group6_prize", "type": "INTEGER", "not_null": true, "primary_key": false}, {"name": "unknown_field1", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "unknown_field2", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "unknown_field3", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "sum_value", "type": "INTEGER", "not_null": true, "primary_key": false}, {"name": "trial_sum_value", "type": "INTEGER", "not_null": true, "primary_key": false}, {"name": "span_value", "type": "INTEGER", "not_null": true, "primary_key": false}, {"name": "trial_span_value", "type": "INTEGER", "not_null": true, "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "primary_key": false}, {"name": "updated_at", "type": "TIMESTAMP", "not_null": false, "primary_key": false}], "record_count": 0}, "hundreds_predictions": {"columns": [{"name": "id", "type": "INTEGER", "not_null": false, "primary_key": true}, {"name": "issue", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "model_type", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "prob_0", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "prob_1", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "prob_2", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "prob_3", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "prob_4", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "prob_5", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "prob_6", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "prob_7", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "prob_8", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "prob_9", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "predicted_digit", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "confidence", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "feature_count", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "training_samples", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "primary_key": false}], "record_count": 1}, "hundreds_model_performance": {"columns": [{"name": "id", "type": "INTEGER", "not_null": false, "primary_key": true}, {"name": "model_type", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "evaluation_period", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "accuracy", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "top3_accuracy", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "avg_confidence", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "precision_per_digit", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "recall_per_digit", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "f1_score_per_digit", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "confusion_matrix", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "feature_importance", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "training_time", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "prediction_time", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "model_size", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "evaluated_at", "type": "TIMESTAMP", "not_null": false, "primary_key": false}], "record_count": 8}, "tens_predictions": {"columns": [{"name": "id", "type": "INTEGER", "not_null": false, "primary_key": true}, {"name": "issue", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "model_type", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "prob_0", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "prob_1", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "prob_2", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "prob_3", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "prob_4", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "prob_5", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "prob_6", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "prob_7", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "prob_8", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "prob_9", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "predicted_digit", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "confidence", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "feature_count", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "training_samples", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "primary_key": false}], "record_count": 0}, "tens_model_performance": {"columns": [{"name": "id", "type": "INTEGER", "not_null": false, "primary_key": true}, {"name": "model_type", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "evaluation_period", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "accuracy", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "top3_accuracy", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "avg_confidence", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "precision_per_digit", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "recall_per_digit", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "f1_score_per_digit", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "confusion_matrix", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "feature_importance", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "training_time", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "prediction_time", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "model_size", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "evaluated_at", "type": "TIMESTAMP", "not_null": false, "primary_key": false}], "record_count": 0}, "units_predictions": {"columns": [{"name": "id", "type": "INTEGER", "not_null": false, "primary_key": true}, {"name": "issue", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "model_type", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "prob_0", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "prob_1", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "prob_2", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "prob_3", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "prob_4", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "prob_5", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "prob_6", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "prob_7", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "prob_8", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "prob_9", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "predicted_digit", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "confidence", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "feature_count", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "training_samples", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "primary_key": false}], "record_count": 0}, "units_model_performance": {"columns": [{"name": "id", "type": "INTEGER", "not_null": false, "primary_key": true}, {"name": "model_type", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "evaluation_period", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "accuracy", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "top3_accuracy", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "avg_confidence", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "precision_per_digit", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "recall_per_digit", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "f1_score_per_digit", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "confusion_matrix", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "feature_importance", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "training_time", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "prediction_time", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "model_size", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "evaluated_at", "type": "TIMESTAMP", "not_null": false, "primary_key": false}], "record_count": 0}, "sum_predictions": {"columns": [{"name": "id", "type": "INTEGER", "not_null": false, "primary_key": true}, {"name": "issue", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "model_type", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "predicted_digit", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "confidence", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "probabilities", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "prediction_range_min", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "prediction_range_max", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "distribution_entropy", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "constraint_score", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "primary_key": false}], "record_count": 0}, "sum_model_performance": {"columns": [{"name": "id", "type": "INTEGER", "not_null": false, "primary_key": true}, {"name": "model_type", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "evaluation_period", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "accuracy", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "mae", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "rmse", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "accuracy_1", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "accuracy_2", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "r2_score", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "distribution_accuracy", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "avg_confidence", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "training_time", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "prediction_time", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "model_size", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "evaluated_at", "type": "TIMESTAMP", "not_null": false, "primary_key": false}], "record_count": 0}, "sum_distribution_stats": {"columns": [{"name": "id", "type": "INTEGER", "not_null": false, "primary_key": true}, {"name": "sum_value", "type": "INTEGER", "not_null": true, "primary_key": false}, {"name": "frequency", "type": "INTEGER", "not_null": true, "primary_key": false}, {"name": "probability", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "avg_span", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "common_patterns", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "seasonal_frequency", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "correlation_with_positions", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "updated_at", "type": "TIMESTAMP", "not_null": false, "primary_key": false}], "record_count": 0}, "sum_constraint_rules": {"columns": [{"name": "id", "type": "INTEGER", "not_null": false, "primary_key": true}, {"name": "rule_name", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "rule_description", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "rule_type", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "min_sum", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "max_sum", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "span_constraint", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "pattern_constraint", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "position_constraint", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "weight", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "priority", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "is_active", "type": "BOOLEAN", "not_null": false, "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "primary_key": false}, {"name": "updated_at", "type": "TIMESTAMP", "not_null": false, "primary_key": false}], "record_count": 0}, "final_predictions": {"columns": [{"name": "id", "type": "INTEGER", "not_null": false, "primary_key": true}, {"name": "issue", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "prediction_rank", "type": "INTEGER", "not_null": true, "primary_key": false}, {"name": "hundreds", "type": "INTEGER", "not_null": true, "primary_key": false}, {"name": "tens", "type": "INTEGER", "not_null": true, "primary_key": false}, {"name": "units", "type": "INTEGER", "not_null": true, "primary_key": false}, {"name": "sum_value", "type": "INTEGER", "not_null": true, "primary_key": false}, {"name": "span_value", "type": "INTEGER", "not_null": true, "primary_key": false}, {"name": "combined_probability", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "hundreds_prob", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "tens_prob", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "units_prob", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "sum_prob", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "span_prob", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "sum_consistency", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "span_consistency", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "constraint_score", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "diversity_score", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "confidence_level", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "fusion_method", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "ranking_strategy", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "primary_key": false}], "record_count": 100}, "fusion_weights": {"columns": [{"name": "id", "type": "INTEGER", "not_null": false, "primary_key": true}, {"name": "weight_type", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "predictor_name", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "model_name", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "weight_value", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "performance_score", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "accuracy_rate", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "confidence_score", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "is_active", "type": "BOOLEAN", "not_null": false, "primary_key": false}, {"name": "last_updated", "type": "TIMESTAMP", "not_null": false, "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "primary_key": false}], "record_count": 6}, "prediction_performance": {"columns": [{"name": "id", "type": "INTEGER", "not_null": false, "primary_key": true}, {"name": "issue", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "actual_hundreds", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "actual_tens", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "actual_units", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "actual_sum", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "actual_span", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "predicted_rank", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "hit_type", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "hundreds_accuracy", "type": "BOOLEAN", "not_null": false, "primary_key": false}, {"name": "tens_accuracy", "type": "BOOLEAN", "not_null": false, "primary_key": false}, {"name": "units_accuracy", "type": "BOOLEAN", "not_null": false, "primary_key": false}, {"name": "sum_accuracy", "type": "BOOLEAN", "not_null": false, "primary_key": false}, {"name": "span_accuracy", "type": "BOOLEAN", "not_null": false, "primary_key": false}, {"name": "overall_score", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "probability_score", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "constraint_score", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "diversity_effectiveness", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "fusion_method", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "ranking_strategy", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "top_k_hit", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "confidence_accuracy", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "evaluated_at", "type": "TIMESTAMP", "not_null": false, "primary_key": false}], "record_count": 0}, "fusion_constraint_rules": {"columns": [{"name": "id", "type": "INTEGER", "not_null": false, "primary_key": true}, {"name": "rule_name", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "rule_type", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "rule_expression", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "weight", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "tolerance", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "is_active", "type": "BOOLEAN", "not_null": false, "primary_key": false}, {"name": "priority", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "primary_key": false}], "record_count": 5}, "fusion_sessions": {"columns": [{"name": "id", "type": "INTEGER", "not_null": false, "primary_key": true}, {"name": "session_id", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "issue", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "fusion_method", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "ranking_strategy", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "input_data", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "output_data", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "execution_time", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "success", "type": "BOOLEAN", "not_null": false, "primary_key": false}, {"name": "error_message", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "primary_key": false}], "record_count": 5}, "weight_history": {"columns": [{"name": "id", "type": "INTEGER", "not_null": false, "primary_key": true}, {"name": "timestamp", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "weights", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "performance_summary", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "adjustment_reason", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "primary_key": false}], "record_count": 0}, "fusion_statistics": {"columns": [{"name": "id", "type": "INTEGER", "not_null": false, "primary_key": true}, {"name": "date", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "total_predictions", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "successful_predictions", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "top_1_hits", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "top_5_hits", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "top_10_hits", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "average_execution_time", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "average_confidence", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "most_used_fusion_method", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "most_used_ranking_strategy", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "primary_key": false}], "record_count": 0}}}}, "performance_references": {"src/web/api_adapter.py": [{"keyword": "enhanced_performance_monitor", "line_number": 19, "line_content": "from src.optimization.enhanced_performance_monitor import EnhancedPerformanceMonitor"}, {"keyword": "enhanced_performance_monitor", "line_number": 187, "line_content": "FROM enhanced_performance_monitor"}, {"keyword": "performance_monitor", "line_number": 19, "line_content": "from src.optimization.enhanced_performance_monitor import EnhancedPerformanceMonitor"}, {"keyword": "performance_monitor", "line_number": 48, "line_content": "self.performance_monitor = EnhancedPerformanceMonitor(db_path)"}, {"keyword": "performance_monitor", "line_number": 55, "line_content": "self.performance_monitor = EnhancedPerformanceMonitor(db_path)"}, {"keyword": "performance_monitor", "line_number": 187, "line_content": "FROM enhanced_performance_monitor"}, {"keyword": "performance_monitor", "line_number": 287, "line_content": "if self.performance_monitor.is_monitoring():"}, {"keyword": "performance_metrics", "line_number": 63, "line_content": "'performance_metrics': await self._get_performance_metrics(),"}, {"keyword": "performance_metrics", "line_number": 72, "line_content": "'performance_metrics': {},"}, {"keyword": "performance_metrics", "line_number": 175, "line_content": "async def _get_performance_metrics(self) -> Dict[str, Any]:"}, {"keyword": "performance_metrics", "line_number": 220, "line_content": "def _get_mock_performance_metrics(self) -> Dict[str, Any]:"}, {"keyword": "monitoring", "line_number": 36, "line_content": "def is_monitoring(self): return False"}, {"keyword": "monitoring", "line_number": 287, "line_content": "if self.performance_monitor.is_monitoring():"}], "src/optimization/intelligent_closed_loop_optimizer.py": [{"keyword": "performance_monitor", "line_number": 82, "line_content": "self.performance_monitor = None"}, {"keyword": "performance_monitor", "line_number": 142, "line_content": "from ..fusion.performance_monitor import PerformanceMonitor"}, {"keyword": "performance_monitor", "line_number": 153, "line_content": "self.performance_monitor = PerformanceMonitor(self.db_path, monitor_config)"}, {"keyword": "performance_monitor", "line_number": 285, "line_content": "if not self.performance_monitor:"}, {"keyword": "performance_monitor", "line_number": 398, "line_content": "if self.performance_monitor:"}, {"keyword": "performance_monitor", "line_number": 399, "line_content": "return self.performance_monitor.get_recent_performance()"}, {"keyword": "performance_monitor", "line_number": 461, "line_content": "'performance_monitor': self.performance_monitor is not None,"}, {"keyword": "monitoring", "line_number": 150, "line_content": "'monitoring_interval': 60,"}], "config/p9_config.yaml": [{"keyword": "monitoring", "line_number": 25, "line_content": "monitoring:"}], "config/fusion_config.yaml": [{"keyword": "monitoring", "line_number": 97, "line_content": "monitoring:"}]}, "database_connections": {"src/web/api_adapter.py": [{"pattern": "sqlite3.connect", "line_number": 83, "line_content": "conn = sqlite3.connect(self.db_path)"}, {"pattern": "sqlite3.connect", "line_number": 181, "line_content": "conn = sqlite3.connect(self.db_path)"}, {"pattern": "sqlite3.connect", "line_number": 258, "line_content": "conn = sqlite3.connect(self.db_path)"}], "src/web/app.py": [{"pattern": "fucai3d.db", "line_number": 79, "line_content": "db_path = os.path.join(project_root, \"data\", \"fucai3d.db\")"}, {"pattern": "fucai3d.db", "line_number": 238, "line_content": "if not os.path.exists(\"data/fucai3d.db\"):"}]}, "recommendations": ["建议在主数据库 data/lottery.db 中进行修改"]}