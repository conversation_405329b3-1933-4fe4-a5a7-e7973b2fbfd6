#!/usr/bin/env python3
"""
检查数据库表是否存在
"""

import sqlite3
import os

def check_database_tables():
    """检查数据库表"""
    
    db_path = "data/lottery.db"
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查所有表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        all_tables = cursor.fetchall()
        
        print(f"📊 数据库中共有 {len(all_tables)} 个表:")
        for table in all_tables:
            print(f"  - {table[0]}")
        
        # 检查特定的性能监控表
        tables_to_check = [
            'enhanced_performance_monitor',
            'performance_monitor',
            'final_predictions',
            'fusion_weights',
            'fusion_constraint_rules'
        ]
        
        print(f"\n🔍 检查关键表:")
        for table_name in tables_to_check:
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table_name,))
            result = cursor.fetchall()
            exists = len(result) > 0
            
            if exists:
                # 检查表中的数据量
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                print(f"  ✅ {table_name}: 存在 ({count} 条记录)")
            else:
                print(f"  ❌ {table_name}: 不存在")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 检查数据库时出错: {e}")
        return False

if __name__ == '__main__':
    check_database_tables()
