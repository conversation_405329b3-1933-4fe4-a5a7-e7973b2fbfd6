import React, { useState, useEffect } from 'react'
import { Card, Row, Col, Statistic, Table, Tag, Alert, Spin, Progress, Tooltip } from 'antd'
import { TrophyOutlined, RocketOutlined, CheckCircleOutlined, ClockCircleOutlined, ReloadOutlined } from '@ant-design/icons'
import { usePredictionData } from '../hooks/usePredictionData'
import { useRealTimeData } from '../hooks/useRealTimeData'
import ProbabilityChart from './ProbabilityChart'
import RecommendationList from './RecommendationList'
import WebSocketStatus from './WebSocketStatus'
import axios from 'axios'

interface PredictionData {
  issue: string
  prediction_rank: number
  hundreds: number
  tens: number
  units: number
  sum_value: number
  span: number
  combined_probability: number
  confidence_level: string
  constraint_score: number
}

interface DashboardData {
  predictions: PredictionData[]
  system_status: any
  performance_metrics: any
  optimization_tasks: any[]
  update_time: string
}

const Dashboard: React.FC = () => {
  const [systemData, setSystemData] = useState<DashboardData | null>(null)
  const [systemLoading, setSystemLoading] = useState(true)
  const [systemError, setSystemError] = useState<string | null>(null)

  // 使用预测数据Hook
  const {
    predictions,
    statistics,
    loading: predictionLoading,
    error: predictionError,
    lastUpdate,
    refreshData,
    getConfidenceColor,
    getConfidenceText,
    formatProbability,
    formatPredictionNumber,
    calculateFeatures
  } = usePredictionData()

  // 使用实时数据Hook
  const {
    data: realTimeData,
    isRealTime,
    wsStatus,
    refreshData: refreshRealTimeData,
    reconnectWebSocket,
    dataFreshness
  } = useRealTimeData({
    enableWebSocket: true,
    onDataUpdate: (data) => {
      // 当实时数据更新时，同步更新本地状态
      if (data.systemStatus) {
        setSystemData(prev => prev ? { ...prev, system_status: data.systemStatus } : null)
      }
    }
  })

  useEffect(() => {
    fetchSystemData()
    // 设置定时刷新
    const interval = setInterval(fetchSystemData, 30000) // 30秒刷新一次
    return () => clearInterval(interval)
  }, [])

  const fetchSystemData = async () => {
    try {
      const response = await axios.get('/api/status')
      if (response.data.status === 'success') {
        setSystemData(response.data.data)
        setSystemError(null)
      } else {
        setSystemError(response.data.message || '获取系统数据失败')
      }
    } catch (err) {
      setSystemError('连接服务器失败，请检查后端服务是否正常运行')
      console.error('获取系统数据失败:', err)
    } finally {
      setSystemLoading(false)
    }
  }

  const handleRefresh = () => {
    refreshData()
    refreshRealTimeData()
    fetchSystemData()
  }



  const predictionColumns = [
    {
      title: '排名',
      dataIndex: 'prediction_rank',
      key: 'rank',
      width: 60,
      render: (rank: number) => (
        <Tag color={rank <= 3 ? 'gold' : rank <= 10 ? 'blue' : 'default'}>
          {rank}
        </Tag>
      ),
    },
    {
      title: '预测号码',
      key: 'number',
      render: (record: any) => (
        <span style={{
          fontFamily: 'monospace',
          fontSize: '16px',
          fontWeight: 'bold',
          color: record.prediction_rank <= 5 ? '#1890ff' : '#000'
        }}>
          {formatPredictionNumber(record)}
        </span>
      ),
    },
    {
      title: '和值',
      dataIndex: 'sum_value',
      key: 'sum',
      width: 60,
    },
    {
      title: '跨度',
      dataIndex: 'span',
      key: 'span',
      width: 60,
    },
    {
      title: '概率',
      dataIndex: 'combined_probability',
      key: 'probability',
      render: (prob: number) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <Progress
            percent={prob}
            size="small"
            style={{ width: 60 }}
            showInfo={false}
          />
          <span>{formatProbability(prob)}</span>
        </div>
      ),
      width: 120,
    },
    {
      title: '置信度',
      dataIndex: 'confidence_level',
      key: 'confidence',
      render: (level: string) => (
        <Tag color={getConfidenceColor(level)}>
          {getConfidenceText(level)}
        </Tag>
      ),
      width: 80,
    },
    {
      title: '约束分',
      dataIndex: 'constraint_score',
      key: 'constraint',
      render: (score: number) => (
        <Tooltip title="约束条件满足程度">
          {score ? score.toFixed(3) : '--'}
        </Tooltip>
      ),
      width: 80,
    },
  ]

  const loading = predictionLoading || systemLoading
  const error = predictionError || systemError

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <p style={{ marginTop: 16 }}>正在加载仪表板数据...</p>
      </div>
    )
  }

  if (error) {
    return (
      <Alert
        message="数据加载失败"
        description={error}
        type="error"
        showIcon
        action={
          <button onClick={handleRefresh} style={{ border: 'none', background: 'none', color: '#1890ff', cursor: 'pointer' }}>
            <ReloadOutlined /> 重试
          </button>
        }
      />
    )
  }

  const features = calculateFeatures(predictions)

  return (
    <div>
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="预测数量"
              value={predictions?.length || 0}
              prefix={<TrophyOutlined />}
              valueStyle={{ color: '#3f8600' }}
              suffix={statistics?.basic_stats?.total_predictions ? `/ ${statistics.basic_stats.total_predictions}` : ''}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="平均概率"
              value={features?.avgProbability || '--'}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#1890ff' }}
              suffix="%"
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="系统状态"
              value={systemData?.system_status?.database_status === 'connected' ? '正常' : '异常'}
              prefix={<RocketOutlined />}
              valueStyle={{ color: systemData?.system_status?.database_status === 'connected' ? '#3f8600' : '#cf1322' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="最后更新"
              value={lastUpdate ? lastUpdate.toLocaleTimeString() : '--'}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
            <div style={{ marginTop: 8 }}>
              <button
                onClick={handleRefresh}
                style={{
                  border: 'none',
                  background: 'none',
                  color: '#1890ff',
                  cursor: 'pointer',
                  fontSize: '12px'
                }}
              >
                <ReloadOutlined /> 刷新
              </button>
            </div>
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} lg={16}>
          <Card
            title="🎯 最新预测结果"
            extra={
              <div style={{ display: 'flex', gap: 16, alignItems: 'center' }}>
                {features && (
                  <div style={{ fontSize: '12px', color: '#666' }}>
                    平均和值: {features.avgSumValue} | 平均跨度: {features.avgSpan}
                  </div>
                )}
                <Tag color="blue">期号: {predictions[0]?.issue || '--'}</Tag>
              </div>
            }
          >
            <Table
              columns={predictionColumns}
              dataSource={predictions || []}
              rowKey="prediction_rank"
              pagination={{ pageSize: 10, showSizeChanger: false }}
              size="small"
              scroll={{ x: 600 }}
            />
          </Card>
        </Col>
        <Col xs={24} lg={8}>
          <RecommendationList predictions={predictions.slice(0, 5)} />
        </Col>
      </Row>

      <Row gutter={[16, 16]}>
        <Col xs={24} md={8}>
          <Card title="📊 系统状态" size="small">
            <div style={{ padding: '16px 0' }}>
              <p><strong>数据库状态:</strong>
                <Tag color={systemData?.system_status?.database_status === 'connected' ? 'green' : 'red'}>
                  {systemData?.system_status?.database_status || 'unknown'}
                </Tag>
              </p>
              <p><strong>优化运行:</strong>
                <Tag color={systemData?.system_status?.optimization_running ? 'green' : 'orange'}>
                  {systemData?.system_status?.optimization_running ? '运行中' : '停止'}
                </Tag>
              </p>
              <p><strong>系统健康:</strong>
                <Tag color={systemData?.system_status?.system_health === 'healthy' ? 'green' : 'orange'}>
                  {systemData?.system_status?.system_health || 'unknown'}
                </Tag>
              </p>
            </div>
          </Card>
        </Col>
        <Col xs={24} md={8}>
          <Card title="🔧 活跃组件" size="small">
            <div style={{ padding: '16px 0' }}>
              {systemData?.system_status?.active_components?.length > 0 ? (
                systemData.system_status.active_components.map((component: string, index: number) => (
                  <Tag key={index} color="blue" style={{ marginBottom: 8 }}>
                    {component}
                  </Tag>
                ))
              ) : (
                <p style={{ color: '#999' }}>暂无活跃组件</p>
              )}
            </div>
          </Card>
        </Col>
        <Col xs={24} md={8}>
          <ProbabilityChart />
        </Col>
      </Row>
    </div>
  )
}

export default Dashboard
