import { useState, useEffect, useCallback } from 'react'
import axios from 'axios'

export interface PredictionData {
  issue: string
  prediction_rank: number
  hundreds: number
  tens: number
  units: number
  sum_value: number
  span: number
  combined_probability: number
  confidence_level: string
  constraint_score: number
  created_at: string
}

export interface PredictionResponse {
  status: string
  data: PredictionData[]
  count?: number
  issue?: string
  message?: string
}

export interface PredictionStatistics {
  basic_stats: {
    total_issues: number
    total_predictions: number
    avg_probability: number
    last_prediction_time: string
  }
  confidence_distribution: Array<{
    confidence_level: string
    count: number
  }>
  update_time: string
}

export interface ProbabilityDistribution {
  [key: string]: number
  avg_probability: number
  count: number
}

export const usePredictionData = () => {
  const [predictions, setPredictions] = useState<PredictionData[]>([])
  const [statistics, setStatistics] = useState<PredictionStatistics | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null)

  // 获取最新预测数据
  const fetchLatestPredictions = useCallback(async (limit: number = 20) => {
    try {
      setLoading(true)
      setError(null)
      
      const response = await axios.get<PredictionResponse>(`/api/prediction/latest?limit=${limit}`)
      
      if (response.data.status === 'success') {
        setPredictions(response.data.data)
        setLastUpdate(new Date())
      } else {
        setError('获取预测数据失败')
      }
    } catch (err) {
      console.error('获取预测数据失败:', err)
      setError('连接服务器失败，请检查网络连接')
    } finally {
      setLoading(false)
    }
  }, [])

  // 获取统计信息
  const fetchStatistics = useCallback(async () => {
    try {
      const response = await axios.get<{status: string, data: PredictionStatistics}>('/api/prediction/statistics')
      
      if (response.data.status === 'success') {
        setStatistics(response.data.data)
      }
    } catch (err) {
      console.error('获取统计信息失败:', err)
    }
  }, [])

  // 获取概率分布
  const fetchProbabilityDistribution = useCallback(async (position: 'hundreds' | 'tens' | 'units') => {
    try {
      const response = await axios.get<{status: string, data: ProbabilityDistribution[]}>(`/api/prediction/probability-distribution?position=${position}`)
      
      if (response.data.status === 'success') {
        return response.data.data
      }
      return []
    } catch (err) {
      console.error('获取概率分布失败:', err)
      return []
    }
  }, [])

  // 获取历史数据
  const fetchHistoryData = useCallback(async (days: number = 7, page: number = 1, pageSize: number = 50) => {
    try {
      const response = await axios.get(`/api/prediction/history?days=${days}&page=${page}&page_size=${pageSize}`)
      
      if (response.data.status === 'success') {
        return {
          data: response.data.data,
          pagination: response.data.pagination
        }
      }
      return { data: [], pagination: null }
    } catch (err) {
      console.error('获取历史数据失败:', err)
      return { data: [], pagination: null }
    }
  }, [])

  // 刷新所有数据
  const refreshData = useCallback(async () => {
    await Promise.all([
      fetchLatestPredictions(),
      fetchStatistics()
    ])
  }, [fetchLatestPredictions, fetchStatistics])

  // 初始化数据加载
  useEffect(() => {
    refreshData()
  }, [refreshData])

  // 自动刷新机制（优化：降低轮询频率）
  useEffect(() => {
    const interval = setInterval(() => {
      refreshData()
    }, 60000) // 每60秒刷新一次

    return () => clearInterval(interval)
  }, [refreshData])

  // 获取置信度颜色
  const getConfidenceColor = useCallback((level: string) => {
    switch (level) {
      case 'high': return '#52c41a'
      case 'medium': return '#faad14'
      case 'low': return '#ff4d4f'
      default: return '#d9d9d9'
    }
  }, [])

  // 获取置信度文本
  const getConfidenceText = useCallback((level: string) => {
    switch (level) {
      case 'high': return '高'
      case 'medium': return '中'
      case 'low': return '低'
      default: return '未知'
    }
  }, [])

  // 格式化概率为百分比
  const formatProbability = useCallback((probability: number) => {
    return `${probability.toFixed(1)}%`
  }, [])

  // 格式化预测号码
  const formatPredictionNumber = useCallback((prediction: PredictionData) => {
    return `${prediction.hundreds}${prediction.tens}${prediction.units}`
  }, [])

  // 计算预测号码的特征
  const calculateFeatures = useCallback((predictions: PredictionData[]) => {
    if (predictions.length === 0) return null

    const sumValues = predictions.map(p => p.sum_value)
    const spans = predictions.map(p => p.span)
    const probabilities = predictions.map(p => p.combined_probability)

    return {
      avgSumValue: (sumValues.reduce((a, b) => a + b, 0) / sumValues.length).toFixed(1),
      avgSpan: (spans.reduce((a, b) => a + b, 0) / spans.length).toFixed(1),
      avgProbability: (probabilities.reduce((a, b) => a + b, 0) / probabilities.length).toFixed(3),
      maxProbability: Math.max(...probabilities).toFixed(3),
      minProbability: Math.min(...probabilities).toFixed(3)
    }
  }, [])

  return {
    // 数据状态
    predictions,
    statistics,
    loading,
    error,
    lastUpdate,

    // 数据获取方法
    fetchLatestPredictions,
    fetchStatistics,
    fetchProbabilityDistribution,
    fetchHistoryData,
    refreshData,

    // 工具方法
    getConfidenceColor,
    getConfidenceText,
    formatProbability,
    formatPredictionNumber,
    calculateFeatures
  }
}
