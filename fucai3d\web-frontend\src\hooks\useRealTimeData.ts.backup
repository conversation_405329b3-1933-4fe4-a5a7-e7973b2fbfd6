import { useState, useEffect, useCallback } from 'react'
import { useWebSocket, WebSocketMessage } from './useWebSocket'

export interface RealTimeData {
  predictions: any[]
  systemStatus: any
  performanceMetrics: any
  optimizationTasks: any[]
  lastUpdate: Date | null
}

export interface UseRealTimeDataOptions {
  enableWebSocket?: boolean
  fallbackInterval?: number
  onDataUpdate?: (data: RealTimeData) => void
}

export const useRealTimeData = (options: UseRealTimeDataOptions = {}) => {
  const {
    enableWebSocket = true,
    fallbackInterval = 30000, // 30秒
    onDataUpdate
  } = options

  const [data, setData] = useState<RealTimeData>({
    predictions: [],
    systemStatus: null,
    performanceMetrics: null,
    optimizationTasks: [],
    lastUpdate: null
  })

  const [isRealTime, setIsRealTime] = useState(false)

  // WebSocket消息处理
  const handleWebSocketMessage = useCallback((message: WebSocketMessage) => {
    switch (message.type) {
      case 'status_update':
        setData(prevData => {
          const newData = {
            predictions: message.data.predictions || prevData.predictions,
            systemStatus: message.data.system_status || prevData.systemStatus,
            performanceMetrics: message.data.performance_metrics || prevData.performanceMetrics,
            optimizationTasks: message.data.optimization_tasks || prevData.optimizationTasks,
            lastUpdate: new Date()
          }
          onDataUpdate?.(newData)
          return newData
        })
        setIsRealTime(true)
        break

      case 'new_prediction':
        setData(prevData => {
          const newData = {
            ...prevData,
            predictions: message.data.predictions || prevData.predictions,
            lastUpdate: new Date()
          }
          onDataUpdate?.(newData)
          return newData
        })
        break

      case 'task_progress':
        setData(prevData => {
          const updatedTasks = prevData.optimizationTasks.map(task => 
            task.task_id === message.data.task_id 
              ? { ...task, progress: message.data.progress, task_status: message.data.status }
              : task
          )
          const newData = {
            ...prevData,
            optimizationTasks: updatedTasks,
            lastUpdate: new Date()
          }
          onDataUpdate?.(newData)
          return newData
        })
        break

      case 'optimization_status_changed':
        setData(prevData => {
          const newData = {
            ...prevData,
            systemStatus: {
              ...prevData.systemStatus,
              optimization_status: message.data
            },
            lastUpdate: new Date()
          }
          onDataUpdate?.(newData)
          return newData
        })
        break

      default:
        // 其他消息类型暂不处理
        break
    }
  }, [onDataUpdate])

  // WebSocket连接处理
  const handleWebSocketConnect = useCallback(() => {
    console.log('WebSocket已连接，启用实时数据更新')
    setIsRealTime(true)
  }, [])

  const handleWebSocketDisconnect = useCallback(() => {
    console.log('WebSocket已断开，切换到轮询模式')
    setIsRealTime(false)
  }, [])

  // 使用WebSocket
  const { status: wsStatus, reconnect } = useWebSocket({
    autoReconnect: enableWebSocket,
    onMessage: handleWebSocketMessage,
    onConnect: handleWebSocketConnect,
    onDisconnect: handleWebSocketDisconnect
  })

  // 轮询获取数据（WebSocket断开时的备用方案）
  const fetchDataFallback = useCallback(async () => {
    try {
      // 并行获取多个数据源
      const [statusResponse, predictionsResponse, tasksResponse] = await Promise.allSettled([
        fetch('/api/status'),
        fetch('/api/prediction/latest?limit=10'),
        fetch('/api/monitoring/tasks')
      ])

      const newData: Partial<RealTimeData> = { lastUpdate: new Date() }

      // 处理系统状态
      if (statusResponse.status === 'fulfilled' && statusResponse.value.ok) {
        const statusData = await statusResponse.value.json()
        if (statusData.status === 'success') {
          newData.systemStatus = statusData.data.system_status
          newData.performanceMetrics = statusData.data.performance_metrics
        }
      }

      // 处理预测数据
      if (predictionsResponse.status === 'fulfilled' && predictionsResponse.value.ok) {
        const predictionsData = await predictionsResponse.value.json()
        if (predictionsData.status === 'success') {
          newData.predictions = predictionsData.data
        }
      }

      // 处理任务数据
      if (tasksResponse.status === 'fulfilled' && tasksResponse.value.ok) {
        const tasksData = await tasksResponse.value.json()
        if (tasksData.status === 'success') {
          newData.optimizationTasks = tasksData.data
        }
      }

      setData(prevData => {
        const updatedData = { ...prevData, ...newData }
        onDataUpdate?.(updatedData)
        return updatedData
      })

    } catch (error) {
      console.error('轮询获取数据失败:', error)
    }
  }, [onDataUpdate])

  // 手动刷新数据
  const refreshData = useCallback(async () => {
    if (wsStatus.connected) {
      // 如果WebSocket连接正常，发送刷新请求
      // 这里可以发送特定的消息请求服务器推送最新数据
      console.log('通过WebSocket请求数据刷新')
    } else {
      // 否则使用HTTP请求
      await fetchDataFallback()
    }
  }, [wsStatus.connected, fetchDataFallback])

  // 设置轮询（当WebSocket不可用时）
  useEffect(() => {
    if (!enableWebSocket || !wsStatus.connected) {
      const interval = setInterval(fetchDataFallback, fallbackInterval)
      return () => clearInterval(interval)
    }
  }, [enableWebSocket, wsStatus.connected, fallbackInterval, fetchDataFallback])

  // 初始数据加载
  useEffect(() => {
    fetchDataFallback()
  }, [fetchDataFallback])

  // 获取数据新鲜度状态
  const getDataFreshness = useCallback(() => {
    if (!data.lastUpdate) return 'unknown'
    
    const now = new Date()
    const diff = now.getTime() - data.lastUpdate.getTime()
    
    if (diff < 60000) return 'fresh' // 1分钟内
    if (diff < 300000) return 'stale' // 5分钟内
    return 'outdated' // 超过5分钟
  }, [data.lastUpdate])

  return {
    data,
    isRealTime,
    wsStatus,
    refreshData,
    reconnectWebSocket: reconnect,
    dataFreshness: getDataFreshness()
  }
}
